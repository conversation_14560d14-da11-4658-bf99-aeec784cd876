/**
 * Test API endpoint to measure authentication performance
 * Tests role caching and authentication speed improvements
 */

import { authenticateAdminRequest } from '@/lib/admin-auth';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const testResults = {
    timestamp: new Date().toISOString(),
    tests: [],
    summary: {}
  };

  try {
    // Test 1: Authentication speed test
    console.log('[AuthTest] Starting authentication speed test...');
    const authStartTime = Date.now();
    
    const authResult = await authenticateAdminRequest(req);
    const authEndTime = Date.now();
    const authDuration = authEndTime - authStartTime;

    testResults.tests.push({
      name: 'Authentication Speed',
      duration: authDuration,
      success: authResult.authorized,
      details: {
        user: authResult.user?.email || 'No user',
        role: authResult.role || 'No role',
        error: authResult.error?.message || null
      }
    });

    // Test 2: Role caching test (multiple calls)
    console.log('[AuthTest] Testing role caching with multiple calls...');
    const cacheTestResults = [];
    
    for (let i = 0; i < 3; i++) {
      const cacheStartTime = Date.now();
      const cacheResult = await authenticateAdminRequest(req);
      const cacheDuration = Date.now() - cacheStartTime;
      
      cacheTestResults.push({
        attempt: i + 1,
        duration: cacheDuration,
        success: cacheResult.authorized,
        cached: cacheDuration < 100 // Assume cached if very fast
      });
    }

    testResults.tests.push({
      name: 'Role Caching Performance',
      attempts: cacheTestResults,
      averageDuration: cacheTestResults.reduce((sum, test) => sum + test.duration, 0) / cacheTestResults.length,
      cacheHits: cacheTestResults.filter(test => test.cached).length
    });

    // Test 3: Development bypass check
    testResults.tests.push({
      name: 'Development Mode Check',
      devMode: process.env.NODE_ENV === 'development',
      authBypass: process.env.ENABLE_AUTH_BYPASS === 'true',
      details: 'Checking if development optimizations are active'
    });

    // Summary
    testResults.summary = {
      authenticationWorking: authResult.authorized,
      averageAuthTime: authDuration,
      cacheEffective: cacheTestResults.filter(test => test.cached).length > 0,
      performanceGrade: authDuration < 500 ? 'A' : authDuration < 1000 ? 'B' : authDuration < 2000 ? 'C' : 'D',
      recommendations: []
    };

    if (authDuration > 1000) {
      testResults.summary.recommendations.push('Authentication taking >1s - check database performance');
    }

    if (cacheTestResults.filter(test => test.cached).length === 0) {
      testResults.summary.recommendations.push('Role caching may not be working effectively');
    }

    console.log(`[AuthTest] Tests completed. Auth time: ${authDuration}ms, Grade: ${testResults.summary.performanceGrade}`);

    return res.status(200).json({
      success: true,
      data: testResults
    });

  } catch (error) {
    console.error('[AuthTest] Error during testing:', error);
    
    return res.status(500).json({
      success: false,
      error: 'Authentication test failed',
      details: error.message,
      partialResults: testResults
    });
  }
}
