/**
 * Simple endpoint to check environment variables
 * No authentication required for debugging
 */

export default function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const envCheck = {
    NODE_ENV: process.env.NODE_ENV,
    ENABLE_AUTH_BYPASS: process.env.ENABLE_AUTH_BYPASS,
    NEXT_PUBLIC_ENABLE_AUTH_BYPASS: process.env.NEXT_PUBLIC_ENABLE_AUTH_BYPASS,
    NEXT_PUBLIC_DEV_MODE: process.env.NEXT_PUBLIC_DEV_MODE,
    NEXT_PUBLIC_DEBUG_AUTH: process.env.NEXT_PUBLIC_DEBUG_AUTH,
    timestamp: new Date().toISOString()
  };

  console.log('[EnvCheck] Environment variables:', envCheck);

  return res.status(200).json({
    success: true,
    environment: envCheck,
    authBypassEnabled: process.env.ENABLE_AUTH_BYPASS === 'true',
    developmentMode: process.env.NODE_ENV === 'development'
  });
}
