import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Production Authentication Debugger
 * Minimal debugging component for production environments
 */
export default function ProductionAuthDebugger() {
  const { user, role, loading, error } = useAuth();
  const [isVisible, setIsVisible] = useState(false);
  const [debugInfo, setDebugInfo] = useState({});

  useEffect(() => {
    // Only show in development or when explicitly enabled
    const shouldShow = process.env.NODE_ENV === 'development' || 
                      process.env.NEXT_PUBLIC_DEBUG_AUTH === 'true';
    setIsVisible(shouldShow);

    if (shouldShow) {
      setDebugInfo({
        user: user ? { id: user.id, email: user.email } : null,
        role,
        loading,
        error: error ? error.message : null,
        timestamp: new Date().toISOString()
      });
    }
  }, [user, role, loading, error]);

  // Don't render anything in production unless explicitly enabled
  if (!isVisible) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '10px',
      right: '10px',
      background: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: '8px',
      borderRadius: '4px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px',
      fontFamily: 'monospace'
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
        Auth Debug
      </div>
      <div>User: {debugInfo.user?.email || 'None'}</div>
      <div>Role: {debugInfo.role || 'None'}</div>
      <div>Loading: {debugInfo.loading ? 'Yes' : 'No'}</div>
      {debugInfo.error && (
        <div style={{ color: '#ff6b6b' }}>Error: {debugInfo.error}</div>
      )}
    </div>
  );
}
