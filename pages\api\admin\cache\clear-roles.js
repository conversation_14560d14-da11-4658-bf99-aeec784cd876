/**
 * Admin API endpoint to clear role cache
 * Useful when user roles are updated and cache needs to be invalidated
 */

import { withAdminAuth, clearAllRoleCache, clearUserRoleCache } from '@/lib/admin-auth';

async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId } = req.body;

    if (userId) {
      // Clear cache for specific user
      clearUserRoleCache(userId);
      console.log(`[Cache] Cleared role cache for user: ${userId}`);
      
      return res.status(200).json({
        success: true,
        message: `Role cache cleared for user ${userId}`,
        action: 'user_cache_cleared'
      });
    } else {
      // Clear entire role cache
      clearAllRoleCache();
      console.log(`[Cache] Cleared entire role cache`);
      
      return res.status(200).json({
        success: true,
        message: 'Entire role cache cleared',
        action: 'all_cache_cleared'
      });
    }
  } catch (error) {
    console.error('[Cache] Error clearing role cache:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to clear role cache'
    });
  }
}

export default withAdminAuth(handler);
